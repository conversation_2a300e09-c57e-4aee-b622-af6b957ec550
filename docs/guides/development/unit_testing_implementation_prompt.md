# Unit Testing Implementation Prompt

## Context and Objective

You are tasked with implementing the first unit tests for the FHIR-OMOP project following the established testing standards. This is a foundational task that will set the pattern for all future testing in the project.

## Project Overview

The FHIR-OMOP project is a healthcare data transformation pipeline that converts HL7 FHIR resources to OMOP Common Data Model format. The project is currently in Phase 1 (FHIR server setup) and moving toward Phase 2 (OMOP database implementation).

### Technology Stack
- **Python**: 3.11 with conda environment 'fhir-omop'
- **Testing Framework**: pytest with coverage reporting
- **Database**: PostgreSQL 14 for OMOP CDM v5.4.2
- **FHIR**: HAPI FHIR R4 server
- **Development Approach**: Pedagogical, academic methodology with step-by-step explanations

## Required Reading

Before starting implementation, review these documents:

1. **Primary Standards**: `docs/guides/development/unit_testing_standards.md`
   - Complete testing philosophy and architecture
   - Framework configuration and fixture strategies
   - Mocking patterns and test data management

2. **Code Standards**: `docs/guides/development/standards.md`
   - Python coding conventions
   - Documentation requirements
   - File organization patterns

3. **Project Structure**: Review the current codebase structure, particularly:
   - `src/fhir_omop/` - Main source code
   - `tests/` - Testing directory (may need creation)
   - `pytest.ini` - Testing configuration
   - `tests/conftest.py` - Global fixtures

## Implementation Tasks

### Task 1: Verify and Setup Testing Infrastructure

1. **Verify pytest configuration** exists and follows standards:
   - Check `pytest.ini` configuration
   - Verify test markers are properly defined
   - Ensure coverage reporting is configured

2. **Verify global fixtures** in `tests/conftest.py`:
   - Database connection mocks
   - Environment configuration
   - Sample FHIR and OMOP data fixtures
   - Temporary directory fixtures

3. **Create missing infrastructure** if needed:
   - Test directory structure
   - Module-specific conftest.py files
   - Test data fixtures

### Task 2: Identify Target Module for First Tests

Based on the current codebase, identify the most appropriate module to start with unit testing. Prioritize:

1. **Core business logic modules** (transformation, mapping)
2. **Database interaction modules** (OMOP database operations)
3. **FHIR processing modules** (resource parsing, validation)

**Selection Criteria**:
- Well-defined, isolated functions
- Critical to project functionality
- Minimal external dependencies
- Clear input/output patterns

### Task 3: Implement First Unit Test Suite

For the selected module, create a comprehensive test suite that includes:

#### 3.1 Test Structure
```
tests/test_<selected_module>/
├── __init__.py
├── conftest.py                 # Module-specific fixtures
├── test_<component_1>.py       # Unit tests for first component
├── test_<component_2>.py       # Unit tests for second component
├── test_integration.py         # Integration tests
└── fixtures/
    ├── sample_data.py          # Test data constants
    └── mock_responses.py       # Mock response objects
```

#### 3.2 Test Categories to Implement

1. **Happy Path Tests** (60% of tests):
   - Valid input scenarios
   - Expected successful operations
   - Standard use cases

2. **Error Handling Tests** (25% of tests):
   - Invalid input validation
   - External dependency failures
   - Business logic constraint violations

3. **Edge Cases** (15% of tests):
   - Boundary conditions
   - Empty/null inputs
   - Large dataset scenarios

#### 3.3 Required Test Patterns

Implement examples of each testing pattern from the standards:

1. **Unit Tests with Mocking**:
   ```python
   @pytest.mark.unit
   @patch('module.external_dependency')
   def test_function_with_mocked_dependency(mock_dep):
       """Test function behavior with mocked external dependency."""
       pass
   ```

2. **Integration Tests**:
   ```python
   @pytest.mark.integration
   def test_database_integration(mock_database_connection):
       """Test database interaction with test database."""
       pass
   ```

3. **Error Testing**:
   ```python
   def test_invalid_input_raises_appropriate_error():
       """Test that invalid input raises specific exception with clear message."""
       with pytest.raises(ValueError, match="Expected error message pattern"):
           function_under_test(invalid_input)
   ```

4. **Performance Testing**:
   ```python
   @pytest.mark.slow
   def test_function_performance():
       """Test that function completes within acceptable time."""
       start_time = time.time()
       result = function_under_test(large_dataset)
       execution_time = time.time() - start_time
       assert execution_time < 10.0
   ```

### Task 4: Documentation and Examples

1. **Document the implemented tests** following the standards:
   - Clear test names following `test_<action>_<condition>_<expected_result>` pattern
   - Comprehensive docstrings with Given/When/Then format
   - Inline comments explaining complex test logic

2. **Create test documentation** in `tests/README.md`:
   - How to run the tests
   - Test organization explanation
   - Examples of different test types
   - Troubleshooting common issues

3. **Provide usage examples**:
   - Command examples for running different test categories
   - Coverage report generation
   - Integration with development workflow

### Task 5: Validation and Quality Assurance

1. **Verify test execution**:
   - All tests pass
   - Coverage meets minimum thresholds (80%+ for tested module)
   - Tests run in reasonable time (< 30 seconds for full suite)

2. **Validate test quality**:
   - Tests are isolated and deterministic
   - Mocks are used appropriately
   - Test data is minimal and realistic
   - Error scenarios are properly covered

3. **Check compliance with standards**:
   - Follows established naming conventions
   - Uses appropriate test markers
   - Implements required fixture patterns
   - Documentation meets standards

## Deliverables

### Primary Deliverables

1. **Complete test suite** for selected module with:
   - Minimum 15 unit tests covering core functionality
   - 3-5 integration tests for key workflows
   - 5+ error handling tests
   - 2+ performance/edge case tests

2. **Test infrastructure** including:
   - Module-specific conftest.py with fixtures
   - Test data fixtures and factories
   - Mock objects and responses

3. **Documentation** including:
   - Test suite README with usage instructions
   - Inline documentation for all tests
   - Examples of different test patterns

### Secondary Deliverables

1. **Test execution report** showing:
   - All tests passing
   - Coverage percentage
   - Execution time analysis

2. **Implementation notes** documenting:
   - Module selection rationale
   - Challenges encountered and solutions
   - Recommendations for future test implementation

3. **Template examples** for:
   - Standard test file structure
   - Common fixture patterns
   - Typical test scenarios

## Success Criteria

### Functional Criteria
- [ ] All implemented tests pass consistently
- [ ] Test coverage ≥ 80% for tested module
- [ ] Tests execute in < 30 seconds total
- [ ] No external dependencies in unit tests
- [ ] Integration tests use appropriate mocking

### Quality Criteria
- [ ] Tests follow established naming conventions
- [ ] Comprehensive error scenario coverage
- [ ] Clear, documented test intentions
- [ ] Appropriate use of fixtures and mocks
- [ ] Realistic test data scenarios

### Documentation Criteria
- [ ] Complete test documentation
- [ ] Clear usage instructions
- [ ] Examples of all test patterns
- [ ] Troubleshooting guidance

## Implementation Guidelines

### Development Approach

1. **Start Small**: Begin with the simplest, most isolated functions
2. **Build Incrementally**: Add complexity gradually
3. **Test First Mindset**: Consider testability when reviewing existing code
4. **Document Everything**: Explain decisions and patterns for future developers

### Code Quality

1. **Follow PEP 8**: Maintain consistent Python style
2. **Use Type Hints**: Include type annotations for test functions
3. **Meaningful Names**: Use descriptive test and fixture names
4. **DRY Principle**: Avoid duplication through proper fixture use

### Testing Best Practices

1. **Arrange-Act-Assert**: Structure tests clearly
2. **One Assertion Per Test**: Focus each test on single behavior
3. **Independent Tests**: Ensure tests don't depend on each other
4. **Fast Feedback**: Prioritize quick test execution

## Resources and References

### Official Documentation
- [pytest Documentation](https://docs.pytest.org/)
- [Python unittest.mock](https://docs.python.org/3/library/unittest.mock.html)
- [OMOP CDM v5.4.2](https://ohdsi.github.io/CommonDataModel/)
- [FHIR R4 Specification](https://hl7.org/fhir/R4/)

### Project-Specific Resources
- Project testing standards: `docs/guides/development/unit_testing_standards.md`
- Code standards: `docs/guides/development/standards.md`
- OMOP implementation guide: `docs/guides/omop/`
- FHIR server documentation: `docs/guides/fhir/server/`

### Example Patterns
Review existing project patterns in:
- Environment setup scripts
- Database interaction modules
- FHIR resource processing
- Configuration management

## Notes for Implementation

### Academic Methodology
This project follows a pedagogical approach. Your implementation should:
- Include detailed explanations of testing decisions
- Provide step-by-step reasoning for complex test scenarios
- Reference official documentation and best practices
- Create learning opportunities for future developers

### Flexibility and Evolution
The testing infrastructure should be:
- Adaptable to changing requirements
- Scalable for future modules
- Maintainable with minimal overhead
- Extensible for different test types

### Integration with Project Goals
Remember that this testing foundation will support:
- FHIR to OMOP data transformation
- Healthcare data quality validation
- Integration with OHDSI tools
- Production deployment scenarios

## Questions and Clarifications

If you encounter any ambiguities or need clarification:

1. **Module Selection**: If multiple modules seem equally suitable, prioritize database interaction or core transformation logic
2. **Test Scope**: Focus on unit tests first, add integration tests for critical workflows
3. **Mock Complexity**: Start with simple mocks, increase sophistication as needed
4. **Performance Targets**: Aim for sub-second unit tests, allow longer times for integration tests

## Final Notes

This implementation will serve as the foundation for all future testing in the project. Take time to establish solid patterns that other developers can follow. The goal is not just working tests, but a sustainable testing architecture that supports the project's long-term success.

Remember to commit your work incrementally and document your progress. This foundational work is critical to the project's quality and maintainability.