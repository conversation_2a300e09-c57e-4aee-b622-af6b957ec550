name: fhir-omop
channels:
  - conda-forge
  - defaults
dependencies:
  # Core Python and data processing
  - python=3.11
  - pip
  - numpy
  - pandas
  - sqlalchemy
  - psycopg2-binary
  - requests
  - python-dotenv
  - psutil>=5.8.0  # For system resource monitoring and performance metrics

  # Testing framework and tools
  - pytest>=7.0.0
  - pytest-cov>=4.0.0
  - pytest-mock>=3.10.0

  # Data visualization
  - matplotlib
  - plotly

  # FHIR resources
  - pip:
      - fhir.resources==8.0.0
      - notion-client>=1.0.0
      - tabulate>=0.9.0