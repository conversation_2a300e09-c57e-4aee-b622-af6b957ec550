[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=src/fhir_omop
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-fail-under=80
markers =
    unit: Unit tests (fast, no external dependencies)
    integration: Integration tests (may use test database)
    slow: Tests that take longer than 30 seconds
    external: Tests requiring external services
    omop: Tests specific to OMOP database functionality
    fhir: Tests specific to FHIR processing
