# OMOP Database Server

This directory contains the configuration and scripts to run a local OMOP CDM database using Docker with PostgreSQL. The database follows the official OHDSI OMOP Common Data Model v5.4.2 specifications.

> **Note**: For comprehensive documentation on OMOP CDM, please refer to the [OMOP Documentation](../../../docs/guides/omop/) in the main documentation directory.

## Overview

We have implemented a simplified, containerized OMOP CDM database using Docker with PostgreSQL 14, following the official OHDSI specifications. This setup provides a robust foundation for healthcare data standardization and the FHIR to OMOP transformation pipeline.

## Directory Structure

```
servers/omop-database/
├── .env                        # Local environment variables
├── docker-compose.yml          # Docker Compose configuration
├── manage-omop-database.sh     # Database management script
├── scripts/                    # Setup scripts
│   └── setup/
│       ├── config.py           # Configuration management
│       ├── create_database.py  # Database creation automation
│       ├── database_checker.py # Database status utilities
│       └── load_vocabularies.py # Vocabulary loading
└── README.md                   # This documentation
```

## Prerequisites

- Docker 20.10.x or higher
- Docker Compose 2.x or higher
- Conda environment `fhir-omop` activated
- OMOP vocabularies downloaded from Athena (see [Vocabulary Guide](../../../docs/guides/omop/vocabulary/))

## Configuration

The OMOP database uses a hybrid configuration approach:
- Local `.env` file in this directory for Docker-specific settings
- Main project `.env` file for shared configuration
- Conda environment for Python dependencies

### Environment Variables

The local `.env` file contains Docker-specific overrides:

```bash
# Database Connection (Docker container settings)
OMOP_DB_HOST=omop-postgres
OMOP_DB_PORT=5432
OMOP_DB_NAME=omop_cdm
OMOP_DB_USERNAME=omop
OMOP_DB_PASSWORD=omop_secure_2024

# External Port (to avoid conflicts)
OMOP_EXTERNAL_PORT=5434

# Volume Paths
VOCABULARY_PATH=../../data/vocabulary/omop_v5_20250630
```

> **Security Note**: For production environments, change the default passwords to strong, unique values.

## Usage

### Starting the Database

```bash
# Start the OMOP database
cd servers/omop-database
./manage-omop-database.sh start

# Check database status
./manage-omop-database.sh status

# View database logs
./manage-omop-database.sh logs
```

### Setting up the Database

```bash
# Complete setup (create tables + load vocabularies)
./manage-omop-database.sh setup full

# Create database and tables only
./manage-omop-database.sh setup create-db

# Load vocabularies only (requires existing tables)
./manage-omop-database.sh setup load-vocab
```

### Database Management

```bash
# Open PostgreSQL shell
./manage-omop-database.sh shell

# Stop the database
./manage-omop-database.sh stop

# Restart the database
./manage-omop-database.sh restart

# Clean database (removes all data)
./manage-omop-database.sh clean
```

## Database Schema

The OMOP CDM v5.4.2 includes 39 tables organized into different domains:

- **Standardized Clinical Data**: person, visit_occurrence, condition_occurrence, etc.
- **Standardized Health System Data**: location, provider, care_site
- **Standardized Vocabularies**: concept, vocabulary, concept_relationship, etc.
- **Standardized Metadata**: metadata, cdm_source
- **Standardized Derived Elements**: cohort, cohort_definition

## Vocabulary Loading

The database setup automatically downloads and loads the official OMOP vocabularies:

1. **CONCEPT.csv** - Central metadata table
2. **VOCABULARY.csv** - Vocabulary definitions
3. **CONCEPT_ANCESTOR.csv** - Hierarchical relationships
4. **CONCEPT_RELATIONSHIP.csv** - Concept relationships
5. **RELATIONSHIP.csv** - Relationship types
6. **CONCEPT_SYNONYM.csv** - Alternative names
7. **DOMAIN.csv** - Domain definitions
8. **CONCEPT_CLASS.csv** - Concept classes
9. **DRUG_STRENGTH.csv** - Drug strength data

## Integration with FHIR Server

This OMOP database is designed to work in conjunction with the FHIR server:

```bash
# Start both services
cd servers/fhir-server && ./manage-fhir-server.sh start
cd servers/omop-database && ./manage-omop-database.sh start
```

## Troubleshooting

### Database Connection Issues

```bash
# Check if database is running
./manage-omop-database.sh status

# Check logs for errors
./manage-omop-database.sh logs

# Test connection
./manage-omop-database.sh shell
```

### Vocabulary Loading Issues

```bash
# Check vocabulary path
ls -la data/vocabulary/omop_v5_20250630/

# Run setup with verbose logging
LOG_LEVEL=DEBUG ./manage-omop-database.sh setup load-vocab
```

## Performance Considerations

- **Memory**: Allocate at least 2GB RAM to PostgreSQL container
- **Storage**: OMOP vocabularies require ~5GB disk space
- **Indexing**: Database includes optimized indexes for common queries
- **Batch Processing**: Configurable batch size for large vocabulary loads

## Security

- Database runs in isolated Docker network
- Default passwords should be changed for production
- Consider using Docker secrets for sensitive configuration
- Regular backups recommended for production data

## References

- [OMOP CDM v5.4.2 Documentation](https://ohdsi.github.io/CommonDataModel/)
- [OHDSI DDL Scripts](https://github.com/OHDSI/CommonDataModel/tree/v5.4.2/PostgreSQL)
- [Athena Vocabulary Browser](https://athena.ohdsi.org/)
- [OHDSI Community Forums](https://forums.ohdsi.org/)

---

For more detailed information, please refer to the [project documentation](../../../docs/guides/omop/).
